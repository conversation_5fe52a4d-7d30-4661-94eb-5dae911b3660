export async function loadLocale(lang: string) {
  if (!lang) return {};

  try {
    const response = await fetch(`/locales/${lang}.json`);

    if (!response.ok) {
      console.error(`Failed to load locale ${lang}:`, response.status);
      return {};
    }

    const data = await response.json();
    return data || {};
  } catch (error) {
    console.error('Translation fetch error:', error);
    return {};
  }
}
