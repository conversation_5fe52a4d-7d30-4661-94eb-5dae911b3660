import { defineConfig } from 'vite';
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
import vue from '@vitejs/plugin-vue';
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify';
import { fileURLToPath, URL } from 'node:url';
import path from 'path';
export default defineConfig({ assetsInclude: ['**/*.html'] })

// export default defineConfig({
//     define: {
//         'process.env': {},
//     },
//     plugins: [
//         vue({
//             template: { transformAssetUrls }
//         }),
//         vuetify({
//             autoImport: true,
//         }),
//         VueI18nPlugin({
//             include: path.resolve(__dirname, './src/locales/**'),
//             runtimeOnly: false,
//             compositionOnly: true,
//             fullInstall: true,
//         }),
//     ],
//     resolve: {
//         alias: {
//             '@': fileURLToPath(new URL('./src', import.meta.url))
//         },
//     },
//     server: {
//         port: 5173
//     },
//     base: '/'
// });
