// Remove any path imports if they exist
// ...existing code...

// Replace path.resolve usage with direct path or URL
export async function loadLocale(locale: string) {
  try {
    const response = await fetch(`/api/translations?locale=${locale}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  } catch (error) {
    console.error(`Failed to load locale ${locale}:`, error);
    return {};
  }
}
// ...existing code...
