import { createApp } from 'vue'
import { createPinia } from 'pinia'
import './assets/main.css'
import App from './App.vue'
import router from './router'
import vuetify from './plugins/vuetify'
import intersect from './directives/v-intersect'
import i18n, { setLocale } from './plugins/i18n'

const app = createApp(App)
app.directive('intersect', intersect)

app.use(createPinia())
app.use(router)
app.use(vuetify)
const savedLang = (localStorage.getItem('lang') || 'en') as 'en' | 'mn' | 'ch';

setLocale(savedLang).then(() => {
  app.use(i18n).mount('#app')
})

