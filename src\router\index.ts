
import { createRouter, createWebHistory, RouteRecordRaw  } from 'vue-router';

import HomePage  from '@/pages/HomePage.vue';
import CompanyProfile from '@/pages/CompanyProfile.vue';
import ChairmanSpeech from '@/pages/ChairmanSpeech.vue';
import CorporateCulture from '@/pages/CorporateCulture.vue';
import DevelopmentHistory from '@/pages/DevelopmentHistory.vue';
import PartyBuilding from '@/pages/PartyBuilding.vue';
import JoinUs from '@/pages/JoinUs.vue';
import AluminiumIndustry from '@/pages/AluminiumIndustry.vue';
import CoalIndustry from '@/pages/CoalIndustry.vue';
import ThermalPowerPlate from '@/pages/ThermalPowerPlate.vue';
import ModernAgriculture from '@/pages/ModernAgriculture.vue';
import ModernTradeLogistics from '@/pages/ModernTradeLogistics.vue';
import TechnologyMonte from '@/pages/TechnologyMonte.vue';
import GreenMonte from '@/pages/GreenMonte.vue';
import ResponsibilityMonte from '@/pages/ResponsibilityMonte.vue';
import News from '@/pages/NewsPage.vue';
import NewsDetail from '@/pages/NewsDetail.vue'
import MediaMaterials from '@/pages/MediaMaterials.vue';
import Products from '@/pages/ProductsPage.vue';
import CollaborativeProjects from '@/pages/CollaborativeProjects.vue';
import OfflineTenderAnnouncement from '@/pages/OfflineTenderAnnouncement.vue';
import OfflineDetails from '@/pages/OfflineDetails.vue';
import GreenEcological from '@/pages/GreenEcological.vue';
import GreenGarden from '@/pages/GreenGarden.vue';
import  GreenMine from '@/pages/GreenMine.vue';
import GreenModern from '@/pages/GreenModern.vue';
import ThermalDetailPage from '@/pages/ThermalDetailPage.vue';
import CoalDetailPage from '@/pages/CoalDetailPage.vue';
import PartyBuildingDetail from '@/pages/PartyBuildingDetail.vue';
import StaffStyleDetail from '@/pages/StaffStyleDetail.vue';


const routes: Array<RouteRecordRaw> = [
  { path: '/', component: HomePage },
  { path: '/company-profile', component: CompanyProfile },
  { path: '/chairman-speech', component: ChairmanSpeech },
  { path: '/corporate-culture', component: CorporateCulture },
  { path: '/development-history', component: DevelopmentHistory },
  { path: '/party-building', component: PartyBuilding },
  { path: '/join-us', component: JoinUs },
  { path: '/aluminium-industry-sector', component: AluminiumIndustry },
  { path: '/coal-industry', component: CoalIndustry },
  { path: '/coal-industry/:slug', name: 'CoalDetail', component: CoalDetailPage },
  { path: '/thermal-power-plate', component: ThermalPowerPlate },
  { path: '/modern-agriculture', component: ModernAgriculture },
  { path: '/modern-trade-logistics', component: ModernTradeLogistics },
  { path: '/technology-monte', component: TechnologyMonte },
  { path: '/green-monte', component: GreenMonte },
  { path: '/green/industrial-park', component: GreenModern},
  { path: '/green/green-mine', component: GreenMine },
  { path: '/green/garden-factory', component: GreenGarden },
  { path: '/green/ecological-industry', component: GreenEcological },
  { path: '/responsibility-monte', component: ResponsibilityMonte },
  { path: '/news', component: News },
  { path: '/news/:slug', component: NewsDetail },
  { path: '/media-materials', component: MediaMaterials },
  { path: '/products', component: Products },
  { path: '/collaborative-projects', component: CollaborativeProjects },
  { path: '/offline-tender-announcement', component: OfflineTenderAnnouncement },
  { path: '/offline-tender-announcement/:slug', component: OfflineDetails },
  { path: '/thermal/:slug', name: 'ThermalDetail', component: ThermalDetailPage },
  { path: '/party-building/:slug', name: 'PartyDetail', component: PartyBuildingDetail },
  { path: '/join-us/:slug', name: 'PartyDetail', component: StaffStyleDetail },
];

export default createRouter({
  history: createWebHistory(),
  routes,
});
