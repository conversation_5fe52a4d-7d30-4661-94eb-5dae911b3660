import fs from 'fs'
import path from 'path'
import 'dotenv/config'

import { sql } from '@vercel/postgres'


const localeDir = path.resolve(process.cwd(), 'public', 'locales')

async function upsertToPostgres(lang: string, key: string, value: string) {
  await sql`
    INSERT INTO translations (locale, key, value) VALUES (${lang}, ${key}, ${value})
    ON CONFLICT (locale, key) DO UPDATE SET value = EXCLUDED.value;
  `;
}

async function syncToPostgres(lang: string) {
  console.log(`Syncing ${lang} to Postgres`);
  const filePath = path.resolve(localeDir, `${lang}.json`);
  if (!fs.existsSync(filePath)) {
    console.warn(`File not found: ${filePath}`);
    return;
  }

  const translations = JSON.parse(fs.readFileSync(filePath, 'utf-8'))
  for (const [key, value] of Object.entries<string>(translations)) {
    await upsertToPostgres(lang, key, value);
  }
  console.log(`✅ ${lang} synced to Postgres.`);
}

async function main() {
  const files = fs.readdirSync(localeDir).filter(f => f.endsWith('.json'))
  for (const file of files) {
    const lang = path.basename(file, '.json')
    await syncToPostgres(lang);
  }
  console.log('All done.')
}

main().catch(err => { console.error(err); process.exit(1) })
