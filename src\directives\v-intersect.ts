
import { Directive } from 'vue'

const intersect: Directive = {
  mounted(el, binding) {
    const callback = (entries: IntersectionObserverEntry[]) => {
      if (entries[0].isIntersecting) {
        el.classList.add(binding.value || 'in-view')
        observer.unobserve(el)
      }
    }
    const observer = new IntersectionObserver(callback, { threshold: 0.2 })
    observer.observe(el)
  },
}

export default intersect
